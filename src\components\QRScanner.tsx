'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { QRScannerProps } from '@/types';
import { Camera, Upload, X, Loader2, Shield, AlertCircle } from 'lucide-react';
import { useUITranslation } from '@/utils/ui-translations';
// Removed complex permission dialog - using direct camera access

type ScanMode = 'camera' | 'file' | null;

// ZXing library interface
interface ZXingResult {
  getText(): string;
}

interface ZXingCodeReader {
  decode(binaryBitmap: any, hints?: Map<any, any>): ZXingResult;
  decodeFromVideoDevice(deviceId?: string, videoElement?: HTMLVideoElement): Promise<ZXingResult>;
  decodeFromImageUrl(imageUrl: string): Promise<ZXingResult>;
  decodeFromImageElement(imageElement: HTMLImageElement): Promise<ZXingResult>;
}

export default function QRScanner({
  onScanSuccess,
  onScanError,
  width = 300,
  height = 300
}: QRScannerProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [scanMode, setScanMode] = useState<ScanMode>(null);
  const { t } = useUITranslation();
  const [isScanning, setIsScanning] = useState(false);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [libraryLoaded, setLibraryLoaded] = useState(false);
  // Removed complex permission dialog state - using direct camera access
  const [cameraReady, setCameraReady] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const scanIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const codeReaderRef = useRef<ZXingCodeReader | null>(null);

  // Load ZXing library with better error handling
  useEffect(() => {
    let mounted = true;

    const loadLibrary = async () => {
      try {
        console.log('🔄 Loading ZXing library...');

        // Dynamic import with proper error handling
        const zxingModule = await import('@zxing/library');

        if (!mounted) return;

        // Check if BrowserQRCodeReader exists
        if (!zxingModule.BrowserQRCodeReader) {
          throw new Error('BrowserQRCodeReader not found in ZXing library');
        }

        // Test instantiation
        const testReader = new zxingModule.BrowserQRCodeReader();
        if (!testReader) {
          throw new Error('Failed to instantiate BrowserQRCodeReader');
        }

        setLibraryLoaded(true);
        console.log('✅ ZXing library loaded and tested successfully');
      } catch (err) {
        console.error('❌ Failed to load ZXing library:', err);
        if (mounted) {
          setError(`Failed to load QR scanner library: ${err instanceof Error ? err.message : 'Unknown error'}`);
        }
      }
    };

    loadLibrary();

    return () => {
      mounted = false;
    };
  }, []);

  // Removed camera capabilities check - using direct camera access

  const createCodeReader = useCallback(async (): Promise<ZXingCodeReader | null> => {
    try {
      // Try @zxing/browser first
      try {
        const zxingBrowser = await import('@zxing/browser');
        if (zxingBrowser.BrowserQRCodeReader) {
          return new zxingBrowser.BrowserQRCodeReader();
        }
      } catch (browserErr) {
        console.log('ZXing browser module not available, trying library module');
      }

      // Fallback to @zxing/library
      const zxingLibrary = await import('@zxing/library');
      if (zxingLibrary.BrowserQRCodeReader) {
        return new zxingLibrary.BrowserQRCodeReader();
      }

      throw new Error('No QR code reader available');
    } catch (err) {
      console.error('Failed to create code reader:', err);
      return null;
    }
  }, []);

  const startCameraScanning = useCallback(async (event?: React.MouseEvent) => {
    // Prevent any event bubbling that might interfere
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    console.log('🎥 Camera button clicked');
    console.log('🔍 Debug info:', {
      libraryLoaded,
      isSecureContext: window.isSecureContext,
      hasMediaDevices: !!navigator.mediaDevices,
      hasGetUserMedia: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
      userAgent: navigator.userAgent,
      location: window.location.href,
      protocol: window.location.protocol,
      hostname: window.location.hostname,
      port: window.location.port
    });

    // Additional browser compatibility checks
    console.log('🔍 Browser compatibility:', {
      isChrome: /Chrome/.test(navigator.userAgent),
      isFirefox: /Firefox/.test(navigator.userAgent),
      isSafari: /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent),
      isEdge: /Edge/.test(navigator.userAgent),
      hasPermissionsAPI: 'permissions' in navigator,
      hasEnumerateDevices: !!(navigator.mediaDevices && navigator.mediaDevices.enumerateDevices)
    });

    if (!libraryLoaded) {
      setError('QR scanner library not loaded. Please wait and try again.');
      return;
    }

    // Check if we're in a secure context
    if (!window.isSecureContext) {
      setError('Camera access requires HTTPS or localhost. Please access this site securely.');
      return;
    }

    // Check if MediaDevices API is available
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      setError('Camera API not supported in this browser. Please use a modern browser.');
      return;
    }

    console.log('✅ Basic checks passed, requesting camera access directly');

    // Check available devices first
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const videoDevices = devices.filter(device => device.kind === 'videoinput');
      console.log('📹 Available video devices:', videoDevices.length);
      console.log('📹 Device details:', videoDevices.map(d => ({
        deviceId: d.deviceId,
        label: d.label || 'Unknown camera',
        groupId: d.groupId
      })));

      if (videoDevices.length === 0) {
        setError('No camera devices found on this system.');
        return;
      }
    } catch (e) {
      console.log('📹 Could not enumerate devices:', e);
    }

    // Check current permission state if available
    if ('permissions' in navigator) {
      try {
        const permission = await navigator.permissions.query({ name: 'camera' as PermissionName });
        console.log('📋 Camera permission state:', permission.state);

        // If permission is already denied, show instructions
        if (permission.state === 'denied') {
          setError('Camera access was previously denied. Please click the camera icon in your browser\'s address bar and allow camera access, then refresh the page.');
          return;
        }
      } catch (e) {
        console.log('📋 Permission API not available or failed:', e);
      }
    }

    // Request camera access directly - this will trigger browser's native permission dialog
    try {
      setIsLoading(true);
      setError(null);

      // IMPORTANT: Set scan mode to camera immediately to show the camera UI
      console.log('🔄 Setting scan mode to camera...');
      setScanMode('camera');

      console.log('🎥 Requesting camera access with user gesture...');

      // Add a small delay to ensure user gesture is properly registered
      await new Promise(resolve => setTimeout(resolve, 100));

      // Try with simpler constraints first to increase success rate
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: { ideal: 'environment' }
        }
      });

      console.log('✅ Camera access granted, starting scanner');
      await handlePermissionGranted(mediaStream);

    } catch (error: any) {
      console.error('❌ Camera access denied or failed:', error);
      console.error('❌ Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack,
        code: error.code
      });
      setIsLoading(false);
      setScanMode(null); // Reset to initial state on error

      if (error.name === 'NotAllowedError') {
        setError('Camera access denied. Please click the camera icon in your browser\'s address bar and allow camera access, then try again.');
      } else if (error.name === 'NotFoundError') {
        setError('No camera found on this device.');
      } else if (error.name === 'NotReadableError') {
        setError('Camera is already in use by another application.');
      } else if (error.name === 'OverconstrainedError') {
        setError('Camera constraints not supported. Trying with basic settings...');
        // Try again with simpler constraints
        setTimeout(() => {
          trySimpleCamera();
        }, 1000);
        return;
      } else {
        setError(`Camera error: ${error.message}`);
      }
    }
  }, [libraryLoaded]);

  const trySimpleCamera = async () => {
    try {
      console.log('🎥 Trying simple camera constraints...');
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: true
      });

      console.log('✅ Simple camera access granted');
      await handlePermissionGranted(mediaStream);
    } catch (error: any) {
      console.error('❌ Simple camera access also failed:', error);
      setIsLoading(false);
      setScanMode(null); // Reset to initial state on error
      setError(`Camera access failed: ${error.message}`);
    }
  };

  const handlePermissionGranted = async (mediaStream: MediaStream) => {
    try {
      setError(null);
      // Don't set scanMode here - it's already set in startCameraScanning
      setIsLoading(true);
      setIsScanning(true);
      setCameraReady(false);

      console.log('✅ Camera permission granted, setting up video...');

      setStream(mediaStream);

      if (videoRef.current) {
        const video = videoRef.current;

        console.log('📹 Setting up video element...');

        // Set up video element properties first
        video.muted = true;
        video.playsInline = true;
        video.autoplay = true;

        // Simple approach: just set the stream and let autoplay handle it
        video.srcObject = mediaStream;

        console.log('📹 Video source set, waiting for ready state...');

        // Use a simpler approach - just wait a bit and check if video is working
        let attempts = 0;
        const maxAttempts = 50; // 5 seconds total

        const checkVideoReady = () => {
          return new Promise<void>((resolve, reject) => {
            const checkInterval = setInterval(() => {
              attempts++;

              if (!videoRef.current) {
                clearInterval(checkInterval);
                reject(new Error('Video element lost'));
                return;
              }

              const video = videoRef.current;
              console.log(`📹 Check ${attempts}: readyState=${video.readyState}, videoWidth=${video.videoWidth}, videoHeight=${video.videoHeight}`);

              // Check if video has dimensions (means it's working)
              if (video.videoWidth > 0 && video.videoHeight > 0) {
                clearInterval(checkInterval);
                console.log('✅ Video is ready with dimensions:', video.videoWidth, 'x', video.videoHeight);
                resolve();
                return;
              }

              // Timeout after max attempts
              if (attempts >= maxAttempts) {
                clearInterval(checkInterval);
                reject(new Error('Video setup timeout - no video dimensions'));
                return;
              }
            }, 100); // Check every 100ms
          });
        };

        try {
          await checkVideoReady();
          console.log('✅ Video setup completed successfully');
        } catch (error) {
          console.error('❌ Video setup failed:', error);
          // Try to continue anyway - sometimes video works even without proper dimensions initially
          console.log('⚠️ Continuing with setup despite video check failure...');
        }

        setCameraReady(true);
        setIsLoading(false);
        startScanning();
      }

      console.log('✅ Camera started successfully');
    } catch (err) {
      console.error('❌ Camera setup failed:', err);
      const errorMessage = err instanceof Error ? err.message : 'Camera setup failed';
      setError(errorMessage);
      setIsLoading(false);
      setIsScanning(false);
      setScanMode(null);
      setCameraReady(false);

      // Clean up stream
      if (mediaStream) {
        mediaStream.getTracks().forEach(track => track.stop());
      }
    }
  };

  // Removed handlePermissionDenied - using direct camera access with try/catch

  const startScanning = async () => {
    if (!libraryLoaded || !videoRef.current || !canvasRef.current) return;

    // Create code reader if not exists
    if (!codeReaderRef.current) {
      codeReaderRef.current = await createCodeReader();
    }

    if (!codeReaderRef.current) {
      setError('Failed to initialize QR code reader');
      return;
    }

    const codeReader = codeReaderRef.current;

    const scanFrame = async () => {
      try {
        if (!videoRef.current || !canvasRef.current || !isScanning) return;

        const canvas = canvasRef.current;
        const video = videoRef.current;
        const context = canvas.getContext('2d');

        if (!context) return;

        // Check if video has valid dimensions
        if (video.videoWidth === 0 || video.videoHeight === 0) {
          // Video not ready yet, try again
          if (isScanning) {
            scanIntervalRef.current = setTimeout(scanFrame, 100);
          }
          return;
        }

        // Set canvas size to match video
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;

        // Draw current video frame to canvas
        context.drawImage(video, 0, 0, canvas.width, canvas.height);

        // Try to decode QR code from canvas
        // Create image element from canvas data URL
        const canvasDataUrl = canvas.toDataURL('image/png');
        const imageElement = new Image();

        imageElement.onload = async () => {
          try {
            // Try multiple QR detection methods for camera scanning too
            let result = null;

            // Method 1: Try decodeFromImageElement
            try {
              result = await codeReader.decodeFromImageElement(imageElement);
              if (result && result.getText()) {
                console.log('✅ QR Code scanned (camera):', result.getText());
                onScanSuccess(result.getText());
                stopScanning();
                return;
              }
            } catch (err1) {
              // Try jsQR as fallback for camera scanning
              try {
                const jsQR = await import('jsqr');
                const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
                const jsQrResult = jsQR.default(imageData.data, imageData.width, imageData.height);

                if (jsQrResult && jsQrResult.data) {
                  console.log('✅ QR Code scanned (camera, jsQR):', jsQrResult.data);
                  onScanSuccess(jsQrResult.data);
                  stopScanning();
                  return;
                }
              } catch (err2) {
                // Ignore decode errors (no QR code found), keep scanning
                // Only log unexpected errors
                if (err1 instanceof Error && !err1.message.includes('No QR code found')) {
                  console.debug('Scan frame error (continuing):', err1.message);
                }
              }
            }
          } catch (decodeError) {
            // Ignore decode errors (no QR code found), keep scanning
            // Only log unexpected errors
            if (decodeError instanceof Error && !decodeError.message.includes('No QR code found')) {
              console.debug('Scan frame error (continuing):', decodeError.message);
            }
          }
        };

        imageElement.src = canvasDataUrl;
      } catch (err) {
        // Ignore decode errors (no QR code found), keep scanning
        // Only log unexpected errors
        if (err instanceof Error && !err.message.includes('No QR code found')) {
          console.debug('Scan frame error (continuing):', err.message);
        }
      }

      // Continue scanning if still active
      if (isScanning) {
        scanIntervalRef.current = setTimeout(scanFrame, 150); // Slightly slower for better performance
      }
    };

    // Start scanning
    scanFrame();
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !libraryLoaded) {
      return;
    }

    try {
      setError(null);
      setScanMode('file');
      setIsLoading(true);

      console.log('📁 Processing uploaded file...', {
        name: file.name,
        type: file.type,
        size: file.size
      });

      // Enhanced file validation
      const validImageTypes = [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
        'image/bmp',
        'image/webp'
      ];

      if (!file.type || !validImageTypes.includes(file.type.toLowerCase())) {
        throw new Error(`Unsupported file type: ${file.type}. Please use JPG, PNG, GIF, BMP, or WebP images.`);
      }

      // Check file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        throw new Error('File size too large. Please select an image under 10MB.');
      }

      // Check if file is actually readable
      if (file.size === 0) {
        throw new Error('File appears to be empty. Please select a valid image file.');
      }

      // Create code reader
      const codeReader = await createCodeReader();
      if (!codeReader) {
        throw new Error('Failed to initialize QR code reader');
      }

      // Use FileReader instead of URL.createObjectURL for better compatibility
      const fileReader = new FileReader();

      const processImage = new Promise<void>((resolve, reject) => {
        fileReader.onload = async (e) => {
          try {
            const result = e.target?.result;
            if (!result || typeof result !== 'string') {
              reject(new Error('Failed to read file data'));
              return;
            }

            // Create image element
            const img = new Image();
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            if (!ctx) {
              reject(new Error('Could not get canvas context'));
              return;
            }

            // Set up image loading with timeout
            const imageLoadTimeout = setTimeout(() => {
              reject(new Error('Image loading timed out. Please try a different image.'));
            }, 10000); // 10 second timeout

            img.onload = async () => {
              try {
                clearTimeout(imageLoadTimeout);

                console.log('✅ Image loaded successfully:', {
                  width: img.width,
                  height: img.height
                });

                // Validate image dimensions
                if (img.width === 0 || img.height === 0) {
                  reject(new Error('Invalid image dimensions. Please select a valid image file.'));
                  return;
                }

                // Set canvas size to image size
                canvas.width = img.width;
                canvas.height = img.height;

                // Clear canvas and draw image
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.drawImage(img, 0, 0);

                // Enhance image for better QR detection
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                const data = imageData.data;

                // Simple contrast enhancement
                for (let i = 0; i < data.length; i += 4) {
                  // Convert to grayscale and enhance contrast
                  const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
                  const enhanced = gray > 128 ? 255 : 0; // High contrast black/white
                  data[i] = enhanced;     // Red
                  data[i + 1] = enhanced; // Green
                  data[i + 2] = enhanced; // Blue
                  // Alpha stays the same
                }

                // Put enhanced image data back to canvas
                ctx.putImageData(imageData, 0, 0);

                console.log('🔍 Scanning for QR code...');

                // Create a new image element from the canvas
                const canvasDataUrl = canvas.toDataURL('image/png');
                const imageElement = new Image();

                imageElement.onload = async () => {
                  try {
                    // Try multiple QR detection methods
                    let qrResult = null;
                    let lastError = null;

                    // Method 1: Try decodeFromImageElement
                    try {
                      qrResult = await codeReader.decodeFromImageElement(imageElement);
                      if (qrResult && qrResult.getText()) {
                        console.log('✅ QR Code decoded from file (method 1):', qrResult.getText());
                        onScanSuccess(qrResult.getText());
                        resolve();
                        return;
                      }
                    } catch (err1) {
                      lastError = err1;
                      console.log('Method 1 failed, trying method 2...');
                    }

                    // Method 2: Try with canvas ImageData
                    try {
                      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                      // Try to use decodeFromImageData if available
                      if (typeof (codeReader as any).decodeFromImageData === 'function') {
                        qrResult = await (codeReader as any).decodeFromImageData(imageData);
                        if (qrResult && qrResult.getText()) {
                          console.log('✅ QR Code decoded from file (method 2):', qrResult.getText());
                          onScanSuccess(qrResult.getText());
                          resolve();
                          return;
                        }
                      }
                    } catch (err2) {
                      lastError = err2;
                      console.log('Method 2 failed, trying method 3...');
                    }

                    // Method 3: Try jsQR as fallback
                    try {
                      const jsQR = await import('jsqr');
                      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                      const jsQrResult = jsQR.default(imageData.data, imageData.width, imageData.height);

                      if (jsQrResult && jsQrResult.data) {
                        console.log('✅ QR Code decoded from file (jsQR fallback):', jsQrResult.data);
                        onScanSuccess(jsQrResult.data);
                        resolve();
                        return;
                      }
                    } catch (err3) {
                      lastError = err3;
                      console.log('Method 3 (jsQR) failed');
                    }

                    // If all methods failed
                    console.error('❌ All QR decode methods failed. Last error:', lastError);
                    reject(new Error('No QR code found in the image. Please ensure the image contains a clear, visible QR code.'));
                  } catch (decodeError) {
                    console.error('❌ QR decode error:', decodeError);
                    reject(new Error('No QR code found in the image. Please ensure the image contains a clear, visible QR code.'));
                  }
                };

                imageElement.onerror = () => {
                  reject(new Error('Failed to process the image for QR code scanning.'));
                };

                imageElement.src = canvasDataUrl;
              } catch (err) {
                clearTimeout(imageLoadTimeout);
                console.error('❌ QR code scanning failed:', err);
                if (err instanceof Error && err.message.includes('No MultiFormat Readers')) {
                  reject(new Error(t('qr_scanner_not_initialized')));
                } else {
                  reject(err instanceof Error ? err : new Error('Failed to scan QR code from image'));
                }
              }
            };

            img.onerror = (error) => {
              clearTimeout(imageLoadTimeout);
              console.error('❌ Image loading failed:', error);
              reject(new Error('Failed to load the image. Please check if the file is a valid image format.'));
            };

            // Set image source to trigger loading
            img.src = result;

          } catch (err) {
            console.error('❌ File processing failed:', err);
            reject(err instanceof Error ? err : new Error('Failed to process uploaded file'));
          }
        };

        fileReader.onerror = () => {
          reject(new Error(t('failed_to_read_file')));
        };
      });

      // Start reading the file as data URL
      fileReader.readAsDataURL(file);

      // Wait for processing to complete
      await processImage;

      setScanMode(null);
      setIsLoading(false);
    } catch (err) {
      console.error('❌ File upload error:', err);
      const errorMessage = err instanceof Error ? err.message : 'File upload failed';
      setError(errorMessage);
      if (onScanError) {
        onScanError(errorMessage);
      }
      setScanMode(null);
      setIsLoading(false);
    }

    // Reset file input
    if (event.target) {
      event.target.value = '';
    }
  };



  const stopScanning = useCallback(() => {
    console.log('🛑 Stopping scanner...');

    // Clear scanning interval
    if (scanIntervalRef.current) {
      clearTimeout(scanIntervalRef.current);
      scanIntervalRef.current = null;
    }

    // Stop video stream
    if (stream) {
      stream.getTracks().forEach(track => {
        track.stop();
        console.log('📹 Stopped video track:', track.label);
      });
      setStream(null);
    }

    // Clear video element
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }

    // Clear code reader reference
    codeReaderRef.current = null;

    setIsScanning(false);
    setIsLoading(false);
    setScanMode(null);
  }, [stream]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopScanning();
    };
  }, [stopScanning]);



  if (scanMode === null) {
    return (
      <div className="flex flex-col items-center space-y-6 p-6">
        <div className="text-center">
          <h3 className="text-xl font-semibold text-white mb-2">{t('scan_your_qr_code')}</h3>
          <p className="text-gray-300 text-sm">
            {t('point_camera_qr_description')}
          </p>
        </div>

        {error && (
          <div className="w-full bg-red-500/20 border border-red-500/50 rounded-lg p-4">
            <div className="flex items-center gap-2 text-red-300 mb-2">
              <X size={16} />
              <span className="text-sm font-medium">{t('scanner_error')}</span>
            </div>
            <p className="text-red-200 text-sm mb-3">{error}</p>
            <div className="flex flex-col sm:flex-row gap-2">
              <button
                onClick={() => setError(null)}
                className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded transition-colors"
              >
                {t('try_again')}
              </button>
              {error.includes('Camera') && (
                <p className="text-red-300 text-xs mt-1">
                  {t('try_upload_instead')}
                </p>
              )}
            </div>
          </div>
        )}

        {!libraryLoaded ? (
          <div className="flex flex-col items-center justify-center py-8">
            <Loader2 className="w-8 h-8 text-purple-400 animate-spin mb-4" />
            <p className="text-gray-300">{t('loading_scanner_library')}</p>
          </div>
        ) : (
          <div className="flex flex-col sm:flex-row gap-4 w-full max-w-md">
            <button
              onClick={startCameraScanning}
              className="flex items-center justify-center gap-3 px-6 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 flex-1 shadow-lg bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white"
              title="Click to request camera access"
            >
              <Camera size={20} />
              {t('use_camera')}
            </button>

            <div className="relative flex-1">
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileUpload}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              />
              <button
                className="flex items-center justify-center gap-3 px-6 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 w-full bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700 text-white shadow-lg"
              >
                <Upload size={20} />
                {t('upload_qr_image')}
              </button>
            </div>
          </div>
        )}

        <div className="text-center text-xs text-gray-400 space-y-1">
          <p>📱 {t('qr_code_hint')}</p>
          <p>🖼️ {t('qr_upload_hint')}</p>
          <p>🔒 {t('camera_access_hint')}</p>
        </div>
      </div>
    );
  }

  if (scanMode === 'file' && isLoading) {
    return (
      <div className="flex flex-col items-center space-y-4 p-6">
        <div className="flex items-center justify-between w-full">
          <h3 className="text-lg font-semibold text-white">{t('processing_image')}</h3>
          <button
            onClick={() => {
              setScanMode(null);
              setIsLoading(false);
              setError(null);
            }}
            className="text-gray-400 hover:text-white transition-colors p-2"
          >
            <X size={24} />
          </button>
        </div>

        <div className="flex flex-col items-center justify-center py-8">
          <Loader2 className="w-8 h-8 text-green-400 animate-spin mb-4" />
          <p className="text-gray-300">{t('analyzing_uploaded_image')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center space-y-4 p-6">
      <div className="flex items-center justify-between w-full">
        <h3 className="text-lg font-semibold text-white">
          {isLoading ? t('starting_camera') : t('camera_scanner')}
        </h3>
        <button
          onClick={stopScanning}
          className="text-gray-400 hover:text-white transition-colors p-2"
        >
          <X size={24} />
        </button>
      </div>

      {isLoading && (
        <div className="flex flex-col items-center justify-center py-8">
          <Loader2 className="w-8 h-8 text-purple-400 animate-spin mb-4" />
          <p className="text-gray-300">{t('starting_camera')}</p>
        </div>
      )}

      <div className="relative w-full max-w-sm">
        <video
          ref={videoRef}
          className={`w-full rounded-lg border-2 border-purple-400 ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
          autoPlay
          playsInline
          muted
          style={{ minHeight: '200px' }}
        />
        <canvas
          ref={canvasRef}
          className="hidden"
        />

        {!isLoading && (
          <div className="absolute inset-0 border-2 border-purple-400 rounded-lg pointer-events-none">
            <div className="absolute top-4 left-4 w-6 h-6 border-t-2 border-l-2 border-white"></div>
            <div className="absolute top-4 right-4 w-6 h-6 border-t-2 border-r-2 border-white"></div>
            <div className="absolute bottom-4 left-4 w-6 h-6 border-b-2 border-l-2 border-white"></div>
            <div className="absolute bottom-4 right-4 w-6 h-6 border-b-2 border-r-2 border-white"></div>
          </div>
        )}
      </div>

      {!isLoading && (
        <div className="text-center space-y-2">
          <p className="text-gray-300 text-sm">
            {t('position_qr_code')}
          </p>
          <button
            onClick={() => fileInputRef.current?.click()}
            className="text-green-400 hover:text-green-300 text-sm underline transition-colors"
          >
            {t('upload_image_instead')}
          </button>
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileUpload}
            className="hidden"
          />
        </div>
      )}

      {/* Camera Permission Dialog - Removed, using direct camera access */}
    </div>
  );
}
